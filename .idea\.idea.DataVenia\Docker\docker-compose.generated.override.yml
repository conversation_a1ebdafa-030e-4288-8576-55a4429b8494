# This is a generated file. Not intended for manual editing.
version: "3.8"
services:
  datavenia.api:
    build:
      context: "C:\\Users\\<USER>\\source\\repos\\data-venia-api"
      dockerfile: "DataVenia.Api/Dockerfile"
      target: "build"
      args:
        BUILD_CONFIGURATION: "Debug"
    command: []
    entrypoint:
    - "/opt/JetBrains/RiderDebuggerTools/linux-x64/JetBrains.Debugger.Worker"
    - "--runtimeconfig"
    - "/opt/JetBrains/RiderDebuggerTools/JetBrains.Debugger.Worker.runtimeconfig.json"
    - "--mode=server"
    - "--frontend-port=57000"
    - "--backend-port=57200"
    - "--roslyn-worker-port=57400"
    - "--timeout=60"
    environment:
      ASPNETCORE_STATICWEBASSETS: "/app/bin/Debug/net9.0/DataVenia.Api.staticwebassets.runtime.CT.json"
      RIDER_DEBUGGER_LOG_DIR: "/var/opt/JetBrains/RiderDebuggerTools"
      RESHARPER_LOG_CONF: "/etc/opt/JetBrains/RiderDebuggerTools/backend-log.xml"
    image: "mcr.microsoft.com/dotnet/aspnet:dev"
    ports:
    - "127.0.0.1:57010:57000"
    - "127.0.0.1:57210:57200"
    - "127.0.0.1:57410:57400"
    volumes:
    - "C:\\Users\\<USER>\\source\\repos\\data-venia-api\\DataVenia.Api:/app:rw"
    - "C:\\Users\\<USER>\\source\\repos\\data-venia-api:/src:rw"
    - "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\UserSecrets:/root/.microsoft/usersecrets"
    - "C:\\Users\\<USER>\\.nuget\\packages:/root/.nuget/packages"
    - "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\RiderRemoteDebugger\\2024.3.3\\\
      Linux64:/opt/JetBrains/RiderDebuggerTools"
    - "C:\\Program Files\\JetBrains\\JetBrains Rider 2024.3.3\\bin\\backend-log.xml:/etc/opt/JetBrains/RiderDebuggerTools/backend-log.xml"
    - "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Rider2024.3\\log\\DebuggerWorker\\\
      JetBrains.Debugger.Worker.2025_5_31_15_36_41:/var/opt/JetBrains/RiderDebuggerTools:rw"
    working_dir: "/app"
