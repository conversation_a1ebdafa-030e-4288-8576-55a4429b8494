﻿using DataVenia.Common.Domain;
using System.Text.Json;

namespace DataVenia.Modules.Lawsuits.Domain.Lawsuits;

public sealed class DataDivergence : Entity
{
    private readonly Dictionary<string, DivergentField> _fields = new();

    public Guid Id { get; private set; }
    public string InstanceId { get; private set; }
    public string FieldsJson { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public DateTime? AnalyzedAt { get; private set; }
    public string? AnalyzedBy { get; private set; }
    public bool? WasAccepted { get; private set; }

    public IReadOnlyDictionary<string, DivergentField> Fields
    {
        get
        {
            if (_fields.Count == 0 && !string.IsNullOrEmpty(FieldsJson))
            {
                var deserializedFields = JsonSerializer.Deserialize<Dictionary<string, DivergentField>>(FieldsJson);
                if (deserializedFields != null)
                {
                    foreach (var field in deserializedFields)
                    {
                        _fields[field.Key] = field.Value;
                    }
                }
            }
            return _fields.AsReadOnly();
        }
    }

    private DataDivergence() { }

    public static DataDivergence Create(string instanceId, Dictionary<string, DivergentField> fields)
    {
        var divergence = new DataDivergence
        {
            Id = Guid.CreateVersion7(),
            InstanceId = instanceId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = null,
            AnalyzedAt = null,
            AnalyzedBy = null,
            WasAccepted = null
        };

        foreach (var field in fields)
        {
            divergence._fields[field.Key] = field.Value;
        }

        divergence.FieldsJson = JsonSerializer.Serialize(divergence._fields);
        return divergence;
    }

    public void MarkAsAnalyzed(string analyzedBy, bool wasAccepted)
    {
        AnalyzedAt = DateTime.UtcNow;
        AnalyzedBy = analyzedBy;
        WasAccepted = wasAccepted;
    }

    public void UpdateLastSeenTime()
    {
        UpdatedAt = DateTime.UtcNow;
    }
    

    public bool HasSameEventValue(string fieldName, string eventValue)
    {
        return Fields.TryGetValue(fieldName, out var field) && field.EventValue == eventValue;
    }

    public bool HasAnyFieldWithDifferentEventValue(Dictionary<string, string> newEventValues)
    {
        foreach (var newValue in newEventValues)
        {
            if (!HasSameEventValue(newValue.Key, newValue.Value))
            {
                return true;
            }
        }
        return false;
    }
}

public sealed record DivergentField(string CurrentValue, string EventValue);
