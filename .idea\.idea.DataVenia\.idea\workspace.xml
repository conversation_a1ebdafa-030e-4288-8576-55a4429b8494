<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1cda7fc0-4328-45aa-998a-07d107de4b92" name="Changes" comment="feat: add lawsuit classes and topics seed endpoint">
      <change beforePath="$PROJECT_DIR$/.idea/.idea.DataVenia/Docker/docker-compose.generated.override.yml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.DataVenia/Docker/docker-compose.generated.override.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Api/DataVenia.Api.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Api/DataVenia.Api.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Common.Contracts/Events/LawsuitSync/LawsuitUpdateEvent.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Common.Contracts/Events/LawsuitSync/LawsuitUpdateEvent.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Common.Domain/ResultLibrary/Entity.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Common.Domain/ResultLibrary/Entity.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Common.Infrastructure/DataVenia.Common.Infrastructure.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Common.Infrastructure/DataVenia.Common.Infrastructure.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Common.Infrastructure/InfrastructureConfiguration.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Common.Infrastructure/InfrastructureConfiguration.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Common.Infrastructure/Interceptors/PublishDomainEventsInterceptor.cs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Common.Test/DataVenia.Common.Test.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Common.Test/DataVenia.Common.Test.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Infrastructure/AssociationHubModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Infrastructure/AssociationHubModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Infrastructure/Workers/AssociationProcessingWorker.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Infrastructure/Workers/AssociationProcessingWorker.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Associations/CreateAssociation.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Associations/CreateAssociation.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Associations/GetAllowedAssociations.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Associations/GetAllowedAssociations.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Associations/RemoveAssociation.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Associations/RemoveAssociation.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Associations/ValidateAssociations.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Associations/ValidateAssociations.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Consumers/CreateAssociationEventConsumer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Consumers/CreateAssociationEventConsumer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Consumers/RemoveAssociationEventConsumer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Consumers/RemoveAssociationEventConsumer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Infrastructure/CalendarModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Infrastructure/CalendarModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/CreateAppointment.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/CreateAppointment.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/DeleteAppointment.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/DeleteAppointment.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/GetAppointmentById.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/GetAppointmentById.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/GetAppointments.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/GetAppointments.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/UpdateAppointment.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/UpdateAppointment.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/UpdateAppointmentPatch.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Appointments/UpdateAppointmentPatch.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Status/GetAllStatus.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Status/GetAllStatus.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Status/UpdateStatus.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Calendar.Presentation/Status/UpdateStatus.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Infrastructure/HarveyModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Infrastructure/HarveyModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/CourtDivision/GetCourtDivisions.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/CourtDivision/GetCourtDivisions.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/DataImport/ImportData.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/DataImport/ImportData.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/Forum/GetForums.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/Forum/GetForums.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitClass/GetLawsuitClassById.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitClass/GetLawsuitClassById.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitClass/GetLawsuitClasses.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitClass/GetLawsuitClasses.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitStatus/GetLawsuitStatus.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitStatus/GetLawsuitStatus.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitTopic/GetLawsuitTopicById.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitTopic/GetLawsuitTopicById.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitTopic/GetLawsuitTopics.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitTopic/GetLawsuitTopics.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitType/GetLawsuitTypes.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LawsuitType/GetLawsuitTypes.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LegalCategory/GetLegalCategories.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LegalCategory/GetLegalCategories.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LegalInstance/GetLegalInstances.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/LegalInstance/GetLegalInstances.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/PartyType/GetPartyTypes.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Harvey.Presentation/PartyType/GetPartyTypes.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Application/AppServices/ReceiveLawsuitCallbackAppService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Application/AppServices/ReceiveLawsuitCallbackAppService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Infrastructure/LawsuitSyncModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Infrastructure/LawsuitSyncModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Application/DataVenia.Modules.Lawsuits.Application.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Application/DataVenia.Modules.Lawsuits.Application.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Domain/Lawsuits/DataDivergence.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Domain/Lawsuits/DataDivergence.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Domain/Lawsuits/Lawsuit.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Domain/Lawsuits/Lawsuit.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Domain/LawsuitsData/LawsuitData.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Domain/LawsuitsData/LawsuitData.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Infrastructure/LawsuitsModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Infrastructure/LawsuitsModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Cases/CreateCase.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Cases/CreateCase.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Cases/GetCaseById.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Cases/GetCaseById.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Cases/GetCases.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Cases/GetCases.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Cases/UpdateCase.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Cases/UpdateCase.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Consumers/LawsuitUpdateEventConsumer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Consumers/LawsuitUpdateEventConsumer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/CreateLawsuit.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/CreateLawsuit.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/CreateLawsuitByCnj.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/CreateLawsuitByCnj.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/GetLawsuitById.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/GetLawsuitById.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/GetLawsuitStepsByLawsuitId.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/GetLawsuitStepsByLawsuitId.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/GetLawsuits.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/GetLawsuits.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/UpdateLawsuit.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/UpdateLawsuit.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/UpdateMonitoring.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Lawsuits/UpdateMonitoring.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Domain/Lawyer/Lawyer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Domain/Lawyer/Lawyer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Infrastructure/Database/Migrations/UsersDbContextModelSnapshot.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Infrastructure/Database/Migrations/UsersDbContextModelSnapshot.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Infrastructure/UsersModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Infrastructure/UsersModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/CreateClient.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/CreateClient.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/DeleteClient.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/DeleteClient.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/GetClientById.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/GetClientById.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/GetClients.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/GetClients.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/UpdateClientPatch.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Client/UpdateClientPatch.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/ClientCompany/CreateClientCompany.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/ClientCompany/CreateClientCompany.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/ClientCompany/DeleteClientCompany.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/ClientCompany/DeleteClientCompany.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/ClientCompany/GetClientCompanies.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/ClientCompany/GetClientCompanies.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/ClientCompany/UpdateClientCompany.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/ClientCompany/UpdateClientCompany.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/CreateCompany.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/CreateCompany.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/DeleteCompany.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/DeleteCompany.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/GetCompanies.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/GetCompanies.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/GetCompanyById.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/GetCompanyById.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/UpdateCompanyPatch.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Company/UpdateCompanyPatch.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/CompleteLawyerSignUp.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/CompleteLawyerSignUp.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/GetInvitesToOffices.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/GetInvitesToOffices.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/GetLawyerBySignUpToken.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/GetLawyerBySignUpToken.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/GetLawyerProfile.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/GetLawyerProfile.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/GetLawyers.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/GetLawyers.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/StartLawyerSignUp.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/StartLawyerSignUp.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/UpdateLawyer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Lawyer/UpdateLawyer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Office/AcceptInviteToOffice.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Office/AcceptInviteToOffice.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Office/CreateOffice.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Office/CreateOffice.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Office/InviteLawyerToOffice.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/Office/InviteLawyerToOffice.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/SystemAdministrator/RegisterLawyer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/SystemAdministrator/RegisterLawyer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/User/Login.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/User/Login.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/User/ResetPassword.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/User/ResetPassword.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/User/UpdateUserPatch.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataVenia.Modules.Users.Presentation/User/UpdateUserPatch.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/.nuget/packages/microsoft.net.test.sdk/17.12.0/build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/81f470d7433296f1f41327245ceeb713fe80ec1d53983d8b3755a93888d5c13e/HttpRequest.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/cb84b0c6366bb07e1e6831d31e321b5f2dee508d7317ddd6e8e62b6ce82b5d/Enum.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Infrastructure/AssociationHubModule.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Infrastructure/Workers/AssociationProcessingWorker.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250215191528_AddHarveyData.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2wnOeenlAlr9lpvskK0Vn1hk8Jb" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Docker.DataVenia.Api/Dockerfile.executor&quot;: &quot;Debug&quot;,
    &quot;Docker.docker-compose.yml: Compose Deployment.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;XThreadsFramesViewSplitterKey&quot;: &quot;0.61276126&quot;,
    &quot;git-widget-placeholder&quot;: &quot;feat/lawsuit-cover-data&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Docker.docker-compose.yml: Compose Deployment">
    <configuration name="DataVenia.Api: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Api/DataVenia.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Api: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Api/DataVenia.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Modules.LawsuitSync.Presentation: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Presentation/DataVenia.Modules.LawsuitSync.Presentation.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Modules.LawsuitSync.Presentation: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Presentation/DataVenia.Modules.LawsuitSync.Presentation.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Modules.LawsuitSync.Presentation: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/DataVenia.Modules.LawsuitSync.Presentation/DataVenia.Modules.LawsuitSync.Presentation.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="DataVenia.Api/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="datavenia.api" />
          <option name="contextFolderPath" value="C:\Users\<USER>\source\repos\data-venia-api" />
          <option name="publishAllPorts" value="true" />
          <option name="sourceFilePath" value="DataVenia.Api/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-compose.yml" temporary="true">
      <deployment type="docker-compose.yml">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="docker-compose.yml: Compose Deployment" type="docker-deploy" factoryName="docker-compose.yml" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="composeProjectName" value="datavenia" />
          <option name="sourceFilePath" value="docker-compose.yml" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1cda7fc0-4328-45aa-998a-07d107de4b92" name="Changes" comment="" />
      <created>1746673971126</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746673971126</updated>
      <workItem from="1746673972024" duration="8859000" />
      <workItem from="1747193161360" duration="33664000" />
      <workItem from="1747441182972" duration="4747000" />
      <workItem from="1747445964571" duration="57904000" />
      <workItem from="1747942754763" duration="16189000" />
      <workItem from="1748053274254" duration="59144000" />
      <workItem from="1748701292120" duration="12326000" />
    </task>
    <task id="LOCAL-00001" summary="feat: upgrade to .net 9 and change uuid to v7">
      <option name="closed" value="true" />
      <created>1747447450280</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1747447450280</updated>
    </task>
    <task id="LOCAL-00002" summary="fix: add version 9.0 to one last missing place in docker-compose">
      <option name="closed" value="true" />
      <created>1747447863609</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1747447863609</updated>
    </task>
    <task id="LOCAL-00003" summary="feat: add lawsuit classes and topics db tables">
      <option name="closed" value="true" />
      <created>1747581964276</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1747581964276</updated>
    </task>
    <task id="LOCAL-00004" summary="feat: add lawsuit classes and topics seed endpoint">
      <option name="closed" value="true" />
      <created>1747627506303</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1747627506303</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: upgrade to .net 9 and change uuid to v7" />
    <MESSAGE value="fix: add version 9.0 to one last missing place in docker-compose" />
    <MESSAGE value="feat: add lawsuit classes and topics db tables" />
    <MESSAGE value="feat: add lawsuit classes and topics seed endpoint" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: add lawsuit classes and topics seed endpoint" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Harvey.Application/DataImport/DataImportService.cs</url>
          <line>113</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Harvey.Application\DataImport\DataImportService.cs" containingFunctionPresentation="Method 'ImportLawsuitClassesAsync'">
            <startOffsets>
              <option value="4588" />
            </startOffsets>
            <endOffsets>
              <option value="4642" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Harvey.Application/DataImport/DataImportService.cs</url>
          <line>205</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Harvey.Application\DataImport\DataImportService.cs" containingFunctionPresentation="Method 'ImportLawsuitTopicsAsync'">
            <startOffsets>
              <option value="8361" />
            </startOffsets>
            <endOffsets>
              <option value="8415" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Consumers/LawsuitUpdateEventConsumer.cs</url>
          <line>31</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Lawsuits.Presentation\Consumers\LawsuitUpdateEventConsumer.cs" containingFunctionPresentation="Method 'Consume'">
            <startOffsets>
              <option value="1282" />
            </startOffsets>
            <endOffsets>
              <option value="1332" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Consumers/CreateAssociationEventConsumer.cs</url>
          <line>15</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.AssociationHub.Presentation\Consumers\CreateAssociationEventConsumer.cs" containingFunctionPresentation="Method 'Consume'">
            <startOffsets>
              <option value="579" />
            </startOffsets>
            <endOffsets>
              <option value="609" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250215191528_AddHarveyData.cs</url>
          <line>12</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Harvey.Infrastructure\Database\Migrations\20250215191528_AddHarveyData.cs" containingFunctionPresentation="Method 'Up'">
            <startOffsets>
              <option value="339" />
            </startOffsets>
            <endOffsets>
              <option value="1320" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Harvey.Infrastructure/Database/Migrations/20250215191528_AddHarveyData.cs</url>
          <line>11</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Harvey.Infrastructure\Database\Migrations\20250215191528_AddHarveyData.cs" containingFunctionPresentation="Method 'Up'">
            <startOffsets>
              <option value="325" />
            </startOffsets>
            <endOffsets>
              <option value="326" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Presentation/Consumers/RemoveAssociationEventConsumer.cs</url>
          <line>13</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.AssociationHub.Presentation\Consumers\RemoveAssociationEventConsumer.cs" containingFunctionPresentation="Method 'Consume'">
            <startOffsets>
              <option value="564" />
            </startOffsets>
            <endOffsets>
              <option value="594" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.AssociationHub.Infrastructure/Workers/AssociationProcessingWorker.cs</url>
          <line>42</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.AssociationHub.Infrastructure\Workers\AssociationProcessingWorker.cs" containingFunctionPresentation="Method 'ProcessPendingRequestsAsync'">
            <startOffsets>
              <option value="1580" />
            </startOffsets>
            <endOffsets>
              <option value="1632" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataVenia.Modules.Lawsuits.Presentation/Consumers/LawsuitUpdateEventConsumer.cs</url>
          <line>213</line>
          <properties documentPath="C:\Users\<USER>\source\repos\data-venia-api\DataVenia.Modules.Lawsuits.Presentation\Consumers\LawsuitUpdateEventConsumer.cs" containingFunctionPresentation="Method 'Consume'">
            <startOffsets>
              <option value="9388" />
            </startOffsets>
            <endOffsets>
              <option value="9411" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>